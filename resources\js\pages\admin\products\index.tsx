import { useState } from 'react';
import { Head, Link, router, usePage } from '@inertiajs/react';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import AdminLayout from '@/layouts/admin/admin-layout';
import { trans } from '@/lib/utils';
import { type BreadcrumbItem, type SharedData, type Product, type Category } from '@/types';
import { Eye, Edit, Trash2, Plus, Search, MoreHorizontal, Package, Image, Star } from 'lucide-react';
import { toast } from 'sonner';

interface ProductsIndexProps {
    products: {
        data: Product[];
        current_page: number;
        last_page: number;
        per_page: number;
        total: number;
        from: number;
        to: number;
    };
    filters: {
        search: string;
        per_page: number;
        sort_by: string;
        sort_order: string;
        status?: string;
        category?: string;
    };
    categories: Category[];
}

export default function ProductsIndex({ products, filters, categories }: ProductsIndexProps) {
    const { locale } = usePage<SharedData>().props;
    const [search, setSearch] = useState(filters.search || '');
    const [perPage, setPerPage] = useState(filters.per_page || 10);
    const [statusFilter, setStatusFilter] = useState(filters.status || '');
    const [categoryFilter, setCategoryFilter] = useState(filters.category || '');

    const breadcrumbs: BreadcrumbItem[] = [
        { title: trans('dashboard'), href: route('admin.dashboard') },
        { title: trans('products'), href: route('admin.products.index') },
    ];

    const handleSearch = (value: string) => {
        setSearch(value);
        router.get(route('admin.products.index'), { 
            search: value, 
            per_page: perPage,
            status: statusFilter,
            category: categoryFilter
        }, { 
            preserveState: true,
            replace: true 
        });
    };

    const handlePerPageChange = (value: string) => {
        const newPerPage = parseInt(value);
        setPerPage(newPerPage);
        router.get(route('admin.products.index'), { 
            search, 
            per_page: newPerPage,
            status: statusFilter,
            category: categoryFilter
        }, { 
            preserveState: true,
            replace: true 
        });
    };

    const handleStatusFilter = (value: string) => {
        setStatusFilter(value);
        router.get(route('admin.products.index'), {
            search,
            per_page: perPage,
            status: value === 'all' ? '' : value,
            category: categoryFilter
        }, {
            preserveState: true,
            replace: true
        });
    };

    const handleCategoryFilter = (value: string) => {
        setCategoryFilter(value);
        router.get(route('admin.products.index'), {
            search,
            per_page: perPage,
            status: statusFilter,
            category: value === 'all' ? '' : value
        }, {
            preserveState: true,
            replace: true
        });
    };

    const handleSort = (column: string) => {
        const newSortOrder = filters.sort_by === column && filters.sort_order === 'asc' ? 'desc' : 'asc';
        router.get(route('admin.products.index'), {
            ...filters,
            sort_by: column,
            sort_order: newSortOrder
        }, {
            preserveState: true,
            replace: true
        });
    };

    const handleDelete = (product: Product) => {
        router.delete(route('admin.products.destroy', product.id), {
            onSuccess: () => {
                toast.success(trans('product_deleted_successfully'));
            },
            onError: () => {
                toast.error('Failed to delete product');
            }
        });
    };

    const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat(locale, {
            style: 'decimal',
            minimumFractionDigits: 2,
            maximumFractionDigits: 2,
        }).format(amount);
    };

    const getStatusBadge = (status: string) => {
        switch (status) {
            case 'published':
                return <Badge variant="default">{trans('published')}</Badge>;
            case 'draft':
                return <Badge variant="secondary">{trans('draft')}</Badge>;
            default:
                return <Badge variant="outline">{status}</Badge>;
        }
    };

    return (
        <AdminLayout breadcrumbs={breadcrumbs}>
            <Head title={trans('product_management')} />
            
            <div className="container mx-auto px-4 py-6 space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">{trans('product_management')}</h1>
                        <p className="text-muted-foreground">
                            {trans('showing_results', {
                                from: products.from,
                                to: products.to,
                                total: products.total
                            })}
                        </p>
                    </div>
                    <Button asChild>
                        <Link href={route('admin.products.create')}>
                            <Plus className="mr-2 h-4 w-4" />
                            {trans('create_product')}
                        </Link>
                    </Button>
                </div>

                {/* Filters */}
                <Card>
                    <CardHeader>
                        <CardTitle>{trans('filters')}</CardTitle>
                        <CardDescription>
                            {trans('search_and_filter_products')}
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        <div className="flex gap-4 flex-wrap">
                            <div className="flex-1 min-w-64">
                                <div className="relative">
                                    <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
                                    <Input
                                        placeholder={trans('search_products')}
                                        value={search}
                                        onChange={(e) => handleSearch(e.target.value)}
                                        className="pl-10"
                                    />
                                </div>
                            </div>
                            
                            <Select value={statusFilter || 'all'} onValueChange={handleStatusFilter}>
                                <SelectTrigger className="w-40">
                                    <SelectValue placeholder={trans('status')} />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">{trans('all_statuses')}</SelectItem>
                                    <SelectItem value="published">{trans('published')}</SelectItem>
                                    <SelectItem value="draft">{trans('draft')}</SelectItem>
                                </SelectContent>
                            </Select>

                            <Select value={categoryFilter || 'all'} onValueChange={handleCategoryFilter}>
                                <SelectTrigger className="w-48">
                                    <SelectValue placeholder={trans('category')} />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">{trans('all_categories')}</SelectItem>
                                    {categories.map((category) => (
                                        <SelectItem key={category.id} value={category.id.toString()}>
                                            {category.name}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>

                            <Select value={perPage.toString()} onValueChange={handlePerPageChange}>
                                <SelectTrigger className="w-32">
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="10">10 {trans('per_page')}</SelectItem>
                                    <SelectItem value="25">25 {trans('per_page')}</SelectItem>
                                    <SelectItem value="50">50 {trans('per_page')}</SelectItem>
                                    <SelectItem value="100">100 {trans('per_page')}</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                    </CardContent>
                </Card>

                {/* Products Table */}
                <Card>
                    <CardContent className="p-0">
                        <Table>
                            <TableHeader>
                                <TableRow>
                                    <TableHead>{trans('main_image')}</TableHead>
                                    <TableHead 
                                        className="cursor-pointer hover:bg-muted/50"
                                        onClick={() => handleSort('name')}
                                    >
                                        {trans('name')}
                                    </TableHead>
                                    <TableHead>{trans('sku')}</TableHead>
                                    <TableHead 
                                        className="cursor-pointer hover:bg-muted/50"
                                        onClick={() => handleSort('price')}
                                    >
                                        {trans('price')}
                                    </TableHead>
                                    <TableHead 
                                        className="cursor-pointer hover:bg-muted/50"
                                        onClick={() => handleSort('stock_quantity')}
                                    >
                                        {trans('stock')}
                                    </TableHead>
                                    <TableHead>{trans('categories')}</TableHead>
                                    <TableHead>{trans('status')}</TableHead>
                                    <TableHead>{trans('featured')}</TableHead>
                                    <TableHead 
                                        className="cursor-pointer hover:bg-muted/50"
                                        onClick={() => handleSort('created_at')}
                                    >
                                        {trans('created_at')}
                                    </TableHead>
                                    <TableHead className="text-right">{trans('actions')}</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {products.data.length === 0 ? (
                                    <TableRow>
                                        <TableCell colSpan={10} className="text-center py-8">
                                            <div className="flex flex-col items-center gap-2">
                                                <Package className="h-8 w-8 text-muted-foreground" />
                                                <p className="text-muted-foreground">{trans('no_products_found')}</p>
                                            </div>
                                        </TableCell>
                                    </TableRow>
                                ) : (
                                    products.data.map((product) => (
                                        <TableRow key={product.id}>
                                            <TableCell>
                                                {product.image ? (
                                                    <img 
                                                        src={`/${product.image}`} 
                                                        alt={product.name}
                                                        className="w-12 h-12 rounded-md object-cover"
                                                    />
                                                ) : (
                                                    <div className="w-12 h-12 rounded-md bg-muted flex items-center justify-center">
                                                        <Image className="h-6 w-6 text-muted-foreground" />
                                                    </div>
                                                )}
                                            </TableCell>
                                            <TableCell className="font-medium">
                                                <div>
                                                    <div className="font-medium">{product.name}</div>
                                                    <div className="text-sm text-muted-foreground truncate max-w-xs">
                                                        {product.description}
                                                    </div>
                                                </div>
                                            </TableCell>
                                            <TableCell>
                                                <Badge variant="outline" className="font-mono">
                                                    {product.sku}
                                                </Badge>
                                            </TableCell>
                                            <TableCell>
                                                <span className="font-medium">
                                                    {formatCurrency(product.price)} {trans('currency_symbol')}
                                                </span>
                                            </TableCell>
                                            <TableCell>
                                                <Badge variant={product.stock_quantity > 0 ? "default" : "destructive"}>
                                                    {product.stock_quantity}
                                                </Badge>
                                            </TableCell>
                                            <TableCell>
                                                <div className="flex flex-wrap gap-1">
                                                    {product.categories?.slice(0, 2).map((category) => (
                                                        <Badge key={category.id} variant="outline" className="text-xs">
                                                            {category.name}
                                                        </Badge>
                                                    ))}
                                                    {(product.categories?.length || 0) > 2 && (
                                                        <Badge variant="outline" className="text-xs">
                                                            +{(product.categories?.length || 0) - 2}
                                                        </Badge>
                                                    )}
                                                </div>
                                            </TableCell>
                                            <TableCell>
                                                {getStatusBadge(product.status)}
                                            </TableCell>
                                            <TableCell>
                                                {product.is_featured && (
                                                    <Star className="h-4 w-4 text-yellow-500 fill-current" />
                                                )}
                                            </TableCell>
                                            <TableCell>
                                                {new Date(product.created_at).toLocaleDateString(locale)}
                                            </TableCell>
                                            <TableCell className="text-right">
                                                <DropdownMenu>
                                                    <DropdownMenuTrigger asChild>
                                                        <Button variant="ghost" size="sm">
                                                            <MoreHorizontal className="h-4 w-4" />
                                                        </Button>
                                                    </DropdownMenuTrigger>
                                                    <DropdownMenuContent align="end">
                                                        <DropdownMenuItem asChild>
                                                            <Link href={route('admin.products.show', product.id)}>
                                                                <Eye className="mr-2 h-4 w-4" />
                                                                {trans('view_product')}
                                                            </Link>
                                                        </DropdownMenuItem>
                                                        <DropdownMenuItem asChild>
                                                            <Link href={route('admin.products.edit', product.id)}>
                                                                <Edit className="mr-2 h-4 w-4" />
                                                                {trans('edit_product')}
                                                            </Link>
                                                        </DropdownMenuItem>
                                                        <AlertDialog>
                                                            <AlertDialogTrigger asChild>
                                                                <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                                                                    <Trash2 className="mr-2 h-4 w-4" />
                                                                    {trans('delete_product')}
                                                                </DropdownMenuItem>
                                                            </AlertDialogTrigger>
                                                            <AlertDialogContent>
                                                                <AlertDialogHeader>
                                                                    <AlertDialogTitle>{trans('delete_product')}</AlertDialogTitle>
                                                                    <AlertDialogDescription>
                                                                        {trans('delete_product_confirmation')}
                                                                    </AlertDialogDescription>
                                                                </AlertDialogHeader>
                                                                <AlertDialogFooter>
                                                                    <AlertDialogCancel>{trans('cancel')}</AlertDialogCancel>
                                                                    <AlertDialogAction onClick={() => handleDelete(product)}>
                                                                        {trans('delete')}
                                                                    </AlertDialogAction>
                                                                </AlertDialogFooter>
                                                            </AlertDialogContent>
                                                        </AlertDialog>
                                                    </DropdownMenuContent>
                                                </DropdownMenu>
                                            </TableCell>
                                        </TableRow>
                                    ))
                                )}
                            </TableBody>
                        </Table>
                    </CardContent>
                </Card>

                {/* Pagination */}
                {products.last_page > 1 && (
                    <div className="flex items-center justify-between">
                        <div className="text-sm text-muted-foreground">
                            {trans('showing_results', {
                                from: products.from,
                                to: products.to,
                                total: products.total
                            })}
                        </div>
                        <div className="flex gap-2">
                            {products.current_page > 1 && (
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => router.get(route('admin.products.index'), {
                                        ...filters,
                                        page: products.current_page - 1
                                    })}
                                >
                                    {trans('previous')}
                                </Button>
                            )}
                            {products.current_page < products.last_page && (
                                <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => router.get(route('admin.products.index'), {
                                        ...filters,
                                        page: products.current_page + 1
                                    })}
                                >
                                    {trans('next')}
                                </Button>
                            )}
                        </div>
                    </div>
                )}
            </div>
        </AdminLayout>
    );
}
