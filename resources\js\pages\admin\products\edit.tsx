import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, useState } from 'react';
import { Head, Link, useForm } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { MultiSelect, type MultiSelectOption } from '@/components/ui/multi-select';
import AdminLayout from '@/layouts/admin/admin-layout';
import InputError from '@/components/input-error';
import { trans } from '@/lib/utils';
import { type BreadcrumbItem, type Category, type Product } from '@/types';
import { ArrowLeft, Save, Package, FileText, Image, Settings, DollarSign, Hash } from 'lucide-react';
import { toast } from 'sonner';

interface EditProductForm {
    name: string;
    description: string;
    details: string;
    price: number;
    stock_quantity: number;
    is_featured: boolean;
    status: 'draft' | 'published';
    sku: string;
    image: File | null;
    category_ids: number[];
    additional_images: File[];
    [key: string]: string | number | boolean | File | File[] | number[] | null;
}

interface EditProductProps {
    product: Product;
    categories: Category[];
}

export default function EditProduct({ product, categories }: EditProductProps) {
    const [imagePreview, setImagePreview] = useState<string | null>(
        product.image ? `/storage/${product.image}` : null
    );
    const [additionalImagePreviews, setAdditionalImagePreviews] = useState<string[]>([]);

    const breadcrumbs: BreadcrumbItem[] = [
        { title: trans('dashboard'), href: route('admin.dashboard') },
        { title: trans('products'), href: route('admin.products.index') },
        { title: trans('edit_product'), href: route('admin.products.edit', product.id) },
    ];

    const { data, setData, post, processing, errors } = useForm<EditProductForm>({
        name: product.name || '',
        description: product.description || '',
        details: product.details || '',
        price: product.price || 0,
        stock_quantity: product.stock_quantity || 0,
        is_featured: product.is_featured || false,
        status: product.status || 'draft',
        sku: product.sku || '',
        image: null,
        category_ids: product.categories?.map(cat => cat.id) || [],
        additional_images: [],
    });

    const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const file = e.target.files?.[0];
        if (file) {
            setData('image', file);
            const reader = new FileReader();
            reader.onload = (e) => {
                setImagePreview(e.target?.result as string);
            };
            reader.readAsDataURL(file);
        }
    };

    const handleAdditionalImagesChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const files = Array.from(e.target.files || []);
        if (files.length > 0) {
            setData('additional_images', files);
            
            // Create previews
            const previews: string[] = [];
            files.forEach(file => {
                const reader = new FileReader();
                reader.onload = (e) => {
                    previews.push(e.target?.result as string);
                    if (previews.length === files.length) {
                        setAdditionalImagePreviews(previews);
                    }
                };
                reader.readAsDataURL(file);
            });
        }
    };



    const submit: FormEventHandler = (e) => {
        e.preventDefault();

        post(route('admin.products.update', product.id), {
            onSuccess: () => {
                toast.success(trans('product_updated_successfully'));
            },
            onError: () => {
                toast.error('Failed to update product');
            }
        });
    };

    return (
        <AdminLayout breadcrumbs={breadcrumbs}>
            <Head title={trans('edit_product')} />
            
            <div className="container mx-auto px-4 py-6 space-y-6">
                {/* Header */}
                <div className="flex items-center gap-4">
                    <Button variant="outline" size="sm" asChild>
                        <Link href={route('admin.products.index')}>
                            <ArrowLeft className="mr-2 h-4 w-4" />
                            {trans('back')}
                        </Link>
                    </Button>
                    <div>
                        <h1 className="text-3xl font-bold tracking-tight">{trans('edit_product')}</h1>
                        <p className="text-muted-foreground">
                            {trans('edit_product_details')}
                        </p>
                    </div>
                </div>

                <form onSubmit={submit} className="space-y-6">
                    {/* Basic Information */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Package className="h-5 w-5" />
                                {trans('basic_information')}
                            </CardTitle>
                            <CardDescription>
                                {trans('enter_product_basic_details')}
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div className="space-y-2">
                                    <Label htmlFor="name">{trans('product_name')}</Label>
                                    <Input
                                        id="name"
                                        value={data.name}
                                        onChange={(e) => setData('name', e.target.value)}
                                        placeholder={trans('enter_product_name')}
                                        required
                                    />
                                    <InputError message={errors.name} />
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="sku">{trans('sku')}</Label>
                                    <Input
                                        id="sku"
                                        value={data.sku}
                                        onChange={(e) => setData('sku', e.target.value)}
                                        placeholder="PRD-001"
                                        required
                                    />
                                    <InputError message={errors.sku} />
                                    <p className="text-xs text-muted-foreground">
                                        {trans('sku_description')}
                                    </p>
                                </div>
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="description">{trans('description')}</Label>
                                <Textarea
                                    id="description"
                                    value={data.description}
                                    onChange={(e) => setData('description', e.target.value)}
                                    placeholder={trans('enter_product_description')}
                                    rows={3}
                                    required
                                />
                                <InputError message={errors.description} />
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="details">{trans('details')}</Label>
                                <Textarea
                                    id="details"
                                    value={data.details}
                                    onChange={(e) => setData('details', e.target.value)}
                                    placeholder={trans('enter_product_details_optional')}
                                    rows={4}
                                />
                                <InputError message={errors.details} />
                                <p className="text-xs text-muted-foreground">
                                    {trans('product_details_description')}
                                </p>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Pricing & Inventory */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <DollarSign className="h-5 w-5" />
                                {trans('pricing_inventory')}
                            </CardTitle>
                            <CardDescription>
                                {trans('set_product_price_and_stock')}
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div className="space-y-2">
                                    <Label htmlFor="price">{trans('price')}</Label>
                                    <div className="relative">
                                        <Input
                                            id="price"
                                            type="number"
                                            step="0.01"
                                            min="0"
                                            value={data.price}
                                            onChange={(e) => setData('price', parseFloat(e.target.value) || 0)}
                                            placeholder="0.00"
                                            required
                                            className="pr-16"
                                        />
                                        <div className="absolute right-3 top-1/2 -translate-y-1/2 text-sm text-muted-foreground">
                                            {trans('currency_symbol')}
                                        </div>
                                    </div>
                                    <InputError message={errors.price} />
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="stock_quantity">{trans('stock_quantity')}</Label>
                                    <Input
                                        id="stock_quantity"
                                        type="number"
                                        min="0"
                                        value={data.stock_quantity}
                                        onChange={(e) => setData('stock_quantity', parseInt(e.target.value) || 0)}
                                        placeholder="0"
                                        required
                                    />
                                    <InputError message={errors.stock_quantity} />
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Categories */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Hash className="h-5 w-5" />
                                {trans('categories')}
                            </CardTitle>
                            <CardDescription>
                                {trans('select_product_categories')}
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="space-y-2">
                                <MultiSelect
                                    options={categories.map((category): MultiSelectOption => ({
                                        value: category.id,
                                        label: category.name,
                                        disabled: !category.active
                                    }))}
                                    value={data.category_ids}
                                    onValueChange={(values) => setData('category_ids', values as number[])}
                                    placeholder={trans('select_categories')}
                                    searchPlaceholder={trans('search_categories')}
                                    emptyText={trans('no_categories_found')}
                                    maxDisplayed={5}
                                    className="w-full"
                                />
                                <InputError message={errors.category_ids} />
                                {data.category_ids.length === 0 && (
                                    <p className="text-sm text-muted-foreground">
                                        {trans('select_at_least_one_category')}
                                    </p>
                                )}
                                {data.category_ids.length > 0 && (
                                    <p className="text-sm text-muted-foreground">
                                        {data.category_ids.length} {data.category_ids.length === 1 ? trans('category') : trans('categories')} {trans('selected')}
                                    </p>
                                )}
                            </div>
                        </CardContent>
                    </Card>

                    {/* Images */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Image className="h-5 w-5" />
                                {trans('product_images')}
                            </CardTitle>
                            <CardDescription>
                                {trans('upload_product_images')}
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="space-y-2">
                                <Label htmlFor="image">{trans('main_image')}</Label>
                                <Input
                                    id="image"
                                    type="file"
                                    accept="image/*"
                                    onChange={handleImageChange}
                                />
                                <InputError message={errors.image} />
                                <p className="text-xs text-muted-foreground">
                                    {trans('main_image_description')}
                                </p>
                            </div>
                            
                            {imagePreview && (
                                <div className="mt-4">
                                    <Label>{trans('current_main_image')}</Label>
                                    <div className="mt-2">
                                        <img 
                                            src={imagePreview} 
                                            alt="Current" 
                                            className="w-32 h-32 object-cover rounded-md border"
                                        />
                                    </div>
                                </div>
                            )}

                            <div className="space-y-2">
                                <Label htmlFor="additional_images">{trans('additional_images')}</Label>
                                <Input
                                    id="additional_images"
                                    type="file"
                                    accept="image/*"
                                    multiple
                                    onChange={handleAdditionalImagesChange}
                                />
                                <InputError message={errors.additional_images} />
                                <p className="text-xs text-muted-foreground">
                                    {trans('additional_images_description')}
                                </p>
                            </div>

                            {/* Current Additional Images */}
                            {product.images && product.images.length > 0 && (
                                <div className="mt-4">
                                    <Label>{trans('current_additional_images')}</Label>
                                    <div className="mt-2 grid grid-cols-4 gap-2">
                                        {product.images.map((image, index) => (
                                            <img 
                                                key={index}
                                                src={`/storage/${image.url}`} 
                                                alt={`Current ${index + 1}`} 
                                                className="w-20 h-20 object-cover rounded-md border"
                                            />
                                        ))}
                                    </div>
                                </div>
                            )}

                            {additionalImagePreviews.length > 0 && (
                                <div className="mt-4">
                                    <Label>{trans('new_additional_images_preview')}</Label>
                                    <div className="mt-2 grid grid-cols-4 gap-2">
                                        {additionalImagePreviews.map((preview, index) => (
                                            <img 
                                                key={index}
                                                src={preview} 
                                                alt={`Preview ${index + 1}`} 
                                                className="w-20 h-20 object-cover rounded-md border"
                                            />
                                        ))}
                                    </div>
                                </div>
                            )}
                        </CardContent>
                    </Card>

                    {/* Settings */}
                    <Card>
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <Settings className="h-5 w-5" />
                                {trans('product_settings')}
                            </CardTitle>
                            <CardDescription>
                                {trans('configure_product_settings')}
                            </CardDescription>
                        </CardHeader>
                        <CardContent className="space-y-4">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div className="space-y-2">
                                    <Label htmlFor="status">{trans('status')}</Label>
                                    <Select value={data.status} onValueChange={(value: 'draft' | 'published') => setData('status', value)}>
                                        <SelectTrigger>
                                            <SelectValue />
                                        </SelectTrigger>
                                        <SelectContent>
                                            <SelectItem value="draft">{trans('draft')}</SelectItem>
                                            <SelectItem value="published">{trans('published')}</SelectItem>
                                        </SelectContent>
                                    </Select>
                                    <InputError message={errors.status} />
                                </div>

                                <div className="flex items-center space-x-2">
                                    <Switch
                                        id="is_featured"
                                        checked={data.is_featured}
                                        onCheckedChange={(checked) => setData('is_featured', checked)}
                                    />
                                    <Label htmlFor="is_featured">{trans('featured_product')}</Label>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Submit Button */}
                    <div className="flex justify-end gap-4">
                        <Button type="button" variant="outline" asChild>
                            <Link href={route('admin.products.index')}>
                                {trans('cancel')}
                            </Link>
                        </Button>
                        <Button type="submit" disabled={processing}>
                            <Save className="mr-2 h-4 w-4" />
                            {processing ? trans('updating') : trans('update_product')}
                        </Button>
                    </div>
                </form>
            </div>
        </AdminLayout>
    );
}
