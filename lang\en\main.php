<?php 


return [
    "welcome"  => "Welcome",
    "dashboard" => "Dashboard",
    "login" => "Login",
    "logout" => "Logout",
    "register" => "Register",
    "language" => "Language",
    "arabic" => "Arabic",
    "english" => "English",
    "hello" => "Hello",
    "description" => "Here you can write a description for what you want",
    "remember_me" => "Remember me",
    "forgot_password" => "Forgot password",
    "password" => "Password",
    "email" => "Email",
    "name" => "Name",
    'admin_login_description' => 'Please enter your email and password to access the admin panel',
    'admin_login' => 'Admin Login',
    'email_address' => 'Email Address',
    'password' => 'Password',
    'remember_me' => 'Remember Me',
    'forgot_password' => 'Forgot Password',

    // App branding
    'app_name' => 'Souk Morocco',
    'Back to Home' => 'Back to Home',

    // Registration form
    'Create an account' => 'Create an account',
    'Enter your details below to create your account' => 'Enter your details below to create your account',
    'Register' => 'Register',
    'Personal Information' => 'Personal Information',
    'Location & Payment' => 'Location & Payment',
    'Account Setup' => 'Account Setup',
    'Step' => 'Step',
    'of' => 'of',
    'Previous' => 'Previous',
    'Next' => 'Next',
    'Create Account' => 'Create Account',
    'Already have an account?' => 'Already have an account?',
    'Log in' => 'Log in',

    // Form fields
    'First Name' => 'First Name',
    'Last Name' => 'Last Name',
    'Phone Number' => 'Phone Number',
    'Gender' => 'Gender',
    'National ID' => 'National ID',
    'Country' => 'Country',
    'City' => 'City',
    'Payment Method' => 'Payment Method',
    'Bank Name' => 'Bank Name',
    'RIB Number' => 'RIB Number',
    'Email Address' => 'Email Address',
    'Confirm Password' => 'Confirm Password',

    // Form placeholders
    'Enter your first name' => 'Enter your first name',
    'Enter your last name' => 'Enter your last name',
    'Enter your phone number' => 'Enter your phone number',
    'Select your gender' => 'Select your gender',
    'Enter your national ID' => 'Enter your national ID',
    'Enter your country' => 'Enter your country',
    'Enter your city' => 'Enter your city',
    'Select payment method' => 'Select payment method',
    'Enter your bank name' => 'Enter your bank name',
    'Enter your RIB number' => 'Enter your RIB number',
    '<EMAIL>' => '<EMAIL>',
    'Enter your password' => 'Enter your password',
    'Confirm your password' => 'Confirm your password',

    // Gender options
    'Male' => 'Male',
    'Female' => 'Female',
    'Other' => 'Other',

    // Payment methods
    'Bank Transfer' => 'Bank Transfer',
    'Mobile Money' => 'Mobile Money',
    'PayPal' => 'PayPal',
    'Cash Plus' => 'Cash Plus',
    'Preferred Payment Method' => 'Preferred Payment Method',
    'Receive your earnings in cash from any Cash Plus agency in Morocco' => 'Receive your earnings in cash from any Cash Plus agency in Morocco',
    'Receive your earnings directly in your bank account' => 'Receive your earnings directly in your bank account',
    'Bank Account Information' => 'Bank Account Information',
    'Select Bank' => 'Select Bank',

    // Moroccan Banks
    'Attijariwafa Bank' => 'Attijariwafa Bank',
    'BMCE Bank' => 'BMCE Bank',
    'BMCI' => 'BMCI',
    'CIH Bank' => 'CIH Bank',
    'Crédit Agricole du Maroc' => 'Crédit Agricole du Maroc',
    'Société Générale Maroc' => 'Société Générale Maroc',
    'Bank Al-Maghrib' => 'Bank Al-Maghrib',
    'Crédit du Maroc' => 'Crédit du Maroc',
    'Enter your 24-digit RIB number' => 'Enter your 24-digit RIB number',

    // Validation messages
    'Password must be at least 8 characters long' => 'Password must be at least 8 characters long',

    // Login page
    'Log in to your account' => 'Log in to your account',
    'Enter your email and password below to log in' => 'Enter your email and password below to log in',
    'Log in' => 'Log in',
    'Email address' => 'Email address',
    'Password' => 'Password',
    'Forgot password?' => 'Forgot password?',
    'Remember me' => 'Remember me',
    'Don\'t have an account?' => 'Don\'t have an account?',
    'Sign up' => 'Sign up',

    // Forgot password page
    'Forgot password' => 'Forgot password',
    'Enter your email to receive a password reset link' => 'Enter your email to receive a password reset link',
    'Email password reset link' => 'Email password reset link',
    'Or, return to' => 'Or, return to',
    'log in' => 'log in',

    // Registration success/error messages
    'Account created successfully! Welcome to :app_name.' => 'Account created successfully! Welcome to :app_name.',
    'Registration failed. Please try again.' => 'Registration failed. Please try again.',

    // Validation messages
    'The first name field is required.' => 'The first name field is required.',
    'The first name must be at least 2 characters.' => 'The first name must be at least 2 characters.',
    'The last name field is required.' => 'The last name field is required.',
    'The last name must be at least 2 characters.' => 'The last name must be at least 2 characters.',
    'The phone number field is required.' => 'The phone number field is required.',
    'This phone number is already registered.' => 'This phone number is already registered.',
    'Please enter a valid phone number.' => 'Please enter a valid phone number.',
    'The phone number must be at least 10 characters.' => 'The phone number must be at least 10 characters.',
    'Please select your gender.' => 'Please select your gender.',
    'Please select a valid gender option.' => 'Please select a valid gender option.',
    'The national ID field is required.' => 'The national ID field is required.',
    'This national ID is already registered.' => 'This national ID is already registered.',
    'Please enter a valid national ID (letters and numbers only).' => 'Please enter a valid national ID (letters and numbers only).',
    'The national ID must be at least 5 characters.' => 'The national ID must be at least 5 characters.',
    'The country field is required.' => 'The country field is required.',
    'The country name must be at least 2 characters.' => 'The country name must be at least 2 characters.',
    'The city field is required.' => 'The city field is required.',
    'The city name must be at least 2 characters.' => 'The city name must be at least 2 characters.',
    'Please select a payment method.' => 'Please select a payment method.',
    'Please select a valid payment method.' => 'Please select a valid payment method.',
    'The bank name field is required when bank transfer is selected.' => 'The bank name field is required when bank transfer is selected.',
    'The bank name must be at least 2 characters.' => 'The bank name must be at least 2 characters.',
    'The RIB number field is required when bank transfer is selected.' => 'The RIB number field is required when bank transfer is selected.',
    'This RIB number is already registered.' => 'This RIB number is already registered.',
    'Please enter a valid RIB number (numbers and spaces only).' => 'Please enter a valid RIB number (numbers and spaces only).',
    'The RIB number must be at least 16 characters.' => 'The RIB number must be at least 16 characters.',
    'The email field is required.' => 'The email field is required.',
    'Please enter a valid email address.' => 'Please enter a valid email address.',
    'This email address is already registered.' => 'This email address is already registered.',
    'The password field is required.' => 'The password field is required.',
    'The password confirmation does not match.' => 'The password confirmation does not match.',

    // Field names
    'first name' => 'first name',
    'last name' => 'last name',
    'phone number' => 'phone number',
    'gender' => 'gender',
    'national ID' => 'national ID',
    'country' => 'country',
    'city' => 'city',
    'payment method' => 'payment method',
    'bank name' => 'bank name',
    'RIB number' => 'RIB number',
    'email address' => 'email address',
    'password' => 'password',

    // New keys
    'app_name' => 'Souk Maroc',
    'welcome_page_title' => 'Welcome to :app_name',
    'user_menu_open' => 'Open user menu',
    'register_join_now' => 'Join Now',
    'mobile_menu_toggle' => 'Toggle mobile menu',
    'previous_slide' => 'Previous slide',
    'next_slide' => 'Next slide',
    'go_to_slide' => 'Go to slide',

    'nav_home' => 'Home',
    'nav_products' => 'Products',
    'nav_how_it_works' => 'How it Works',
    'nav_about_us' => 'About Us',
    'nav_contact_us' => 'Contact Us',

    'hero_title_1' => 'Buy at Wholesale, Sell at Your Price, Earn the Difference!',
    'hero_subtitle_1' => 'We specialize in providing high-quality products for marketers. Get our products at special prices including delivery, sell at the price you set, and the difference is your profit!',
    'hero_title_2' => 'We Handle Delivery, You Focus on Marketing',
    'hero_subtitle_2' => 'Don’t worry about delivery or logistics. We handle product delivery to your customers across Morocco, while you focus on marketing and earning profits.',
    'hero_title_3' => 'Market to Your Friends or on Social Media',
    'hero_subtitle_3' => 'Share our products with your friends, acquaintances, or on your social media pages. The more you sell, the more you earn - complete freedom in setting your prices!',
    'join_now' => 'Join Now',
    'learn_more' => 'Learn More',

    'popular_products_title' => 'Our Featured Products',
    'popular_products_subtitle' => 'Choose from a variety of high-quality products, set your price, and earn the difference! We handle delivery to all Moroccan cities.',
    'category_all' => 'All',
    'category_electronics' => 'Electronics',
    'category_clothing' => 'Clothing',
    'category_homegoods' => 'Home Goods',
    'category_beauty' => 'Beauty & Care',
    'product_name_1' => 'Modern Smartwatch X100',
    'product_desc_1' => 'Latest generation smartwatch with health tracking and GPS.',
    'product_name_2' => 'Elegant Cotton T-Shirt',
    'product_desc_2' => 'Comfortable and stylish t-shirt made from premium cotton.',
    'product_name_3' => 'Non-Stick Cookware Set',
    'product_desc_3' => 'Complete set for your kitchen, durable and easy to clean.',
    'product_name_4' => 'Organic Face Serum',
    'product_desc_4' => 'Rejuvenate your skin with our natural and organic face serum.',
    'bestseller' => 'Bestseller',
    'new_product' => 'New',
    'select_for_marketing' => 'Select for Marketing',
    'reviews' => 'reviews',
    'your_price' => 'Your Price',
    'suggested' => 'Suggested',
    'estimated_profit' => 'Est. Profit',
    'delivery_included' => 'Delivery Included',
    'view_all_products' => 'View All Products',

    'how_it_works_title' => 'How to Start Marketing With Us',
    'how_it_works_subtitle' => 'Four simple steps to become a successful marketer and achieve excellent profits.',
    'how_step_1_title' => 'Register Your Account',
    'how_step_1_desc' => 'Create a free account and get instant access to our product catalog.',
    'how_step_2_title' => 'Choose Your Products',
    'how_step_2_desc' => 'Browse our products and choose what you want to market at our special marketer prices.',
    'how_step_3_title' => 'Set Your Price & Market',
    'how_step_3_desc' => 'Set the price you want and share the products with your friends or on social media.',
    'how_step_4_title' => 'Send Orders & Earn',
    'how_step_4_desc' => 'When a customer wants to buy, send us the order and you will receive your profit upon successful sale.',

    'about_us_title' => 'About Us',
    'about_who_we_are_title' => 'Who We Are & How We Work',
    'about_who_we_are_desc' => 'We are a Moroccan company specializing in providing high-quality products to marketers at wholesale prices. We believe everyone deserves a chance to earn extra income through marketing, so we offer diverse products at competitive prices with full delivery management.',
    'about_mission_title' => 'Our Mission',
    'about_mission_desc' => 'To provide high-quality products at wholesale prices for marketers, ensuring fast and safe delivery throughout Morocco, enabling everyone to build their own business.',
    'about_commitment_title' => 'Our Commitment',
    'about_commitment_desc' => 'We are committed to providing excellent service through quality-guaranteed products, competitive prices, and reliable delivery. We are your partner in success, not just a supplier.',
    'stats_partners_number' => '2,500+',
    'stats_partners_label' => 'Partner Marketers',
    'stats_products_number' => '500+',
    'stats_products_label' => 'Diverse Products',
    'stats_delivery_number' => '98%',
    'stats_delivery_label' => 'Successful Deliveries',
    'stats_coverage_number' => 'All Cities',
    'stats_coverage_label' => 'Covering Morocco',

    'why_choose_us_title' => 'Why Choose Us as Your Partner?',
    'why_choose_us_subtitle' => 'We don’t just offer products; we provide integrated solutions to help you build a successful business.',
    'why_feature_1_title' => 'Exclusive Wholesale Prices',
    'why_feature_1_desc' => 'Get high-quality products at wholesale prices including delivery, and set your selling price with complete freedom.',
    'why_feature_2_title' => 'We Handle Delivery',
    'why_feature_2_desc' => 'Don’t worry about shipping or delivery; we handle delivering your orders to all Moroccan cities.',
    'why_feature_3_title' => 'Limitless Profits',
    'why_feature_3_desc' => 'The greater the difference between your purchase price and selling price, the greater your profit. No limits or restrictions on earnings.',

    'testimonials_title' => 'Success Stories from Our Marketers',
    'testimonials_subtitle' => 'Hear the experiences of marketers who have achieved excellent profits with us.',
    'testimonial_1_name' => 'Aisha Ezzahra',
    'testimonial_1_role' => 'Marketer from Casablanca',
    'testimonial_1_content' => 'Wholesale prices are excellent and delivery is fast. I started with 5 products and now I earn more than 3000 MAD monthly!',
    'testimonial_2_name' => 'Karim Ben Ali',
    'testimonial_2_role' => 'Marketer from Fez',
    'testimonial_2_content' => 'The freedom to set prices is great. I sell to my friends and sometimes earn 100% on a single product!',
    'testimonial_3_name' => 'Nadia El Kassimi',
    'testimonial_3_role' => 'Marketer from Rabat',
    'testimonial_3_content' => 'I never worry about delivery; they handle everything, and I just focus on selling and profiting.',

    'faq_title' => 'Frequently Asked Questions',
    'faq_subtitle' => 'Answers to the most common questions about working with us as a marketer.',
    'faq_q1' => 'How do I start working with you as a marketer?',
    'faq_a1' => 'Register a free account, browse our products and choose what suits you, then share with your friends or on social media. When someone wants to buy, send us the order with customer information.',
    'faq_q2' => 'How is my profit calculated on each product?',
    'faq_a2' => 'Your profit = Your selling price - Our price to you (including delivery). Example: We sell you a product for 100 MAD including delivery, you sell it for 150 MAD, your profit is 50 MAD.',
    'faq_q3' => 'When and how do I receive my profits?',
    'faq_a3' => 'You receive your profit immediately after the sale is successfully completed and the customer confirms receipt of the product. Bank transfer or Cash Plus is available based on your choice.',
    'faq_q4' => 'Do you handle delivery to all cities?',
    'faq_a4' => 'Yes, we handle the delivery of all orders to all Moroccan cities. The cost is included in the price we sell to you, so don’t worry about shipping or delivery.',

    'contact_us_title' => 'Contact Us',
    'contact_us_subtitle' => 'Have questions about products or marketing? Our team is ready to help you.',
    'contact_form_title' => 'Send Us a Message',
    'form_full_name' => 'Full Name',
    'form_full_name_placeholder' => 'Enter your full name',
    'form_email' => 'Email',
    'form_email_placeholder' => 'Enter your email address',
    'form_phone' => 'Phone Number',
    'form_phone_placeholder' => 'Enter your phone number',
    'form_message' => 'Message',
    'form_message_placeholder' => 'Write your message here...',
    'form_send_message_button' => 'Send Message',
    'contact_info_title' => 'Contact Information',
    'contact_email_label' => 'Email',
    'contact_email_value' => '<EMAIL>',
    'contact_phone_label' => 'Phone',
    'contact_phone_value' => '+212 5XX-XXXXXX',
    'contact_address_label' => 'Address',
    'contact_address_value' => 'Casablanca, Morocco',
    'contact_working_hours_title' => 'Working Hours',
    'contact_hours_monday_friday' => 'Monday - Friday',
    'contact_hours_monday_friday_time' => '9:00 AM - 6:00 PM',
    'contact_hours_saturday' => 'Saturday',
    'contact_hours_saturday_time' => '9:00 AM - 2:00 PM',
    'contact_hours_sunday' => 'Sunday',
    'contact_hours_sunday_time' => 'Closed',

    'final_cta_title' => 'Start Your Business Today!',
    'final_cta_subtitle' => 'Get products at wholesale prices, set your prices, and we handle the delivery. Start earning from day one!',
    'final_cta_button' => 'Join as a Marketer Now',

    'footer_company_description' => 'A Moroccan company specializing in providing high-quality products for marketers at wholesale prices with guaranteed delivery to all Moroccan cities. Your partner in business success.',
    'footer_quick_links' => 'Quick Links',
    'footer_legal' => 'Legal',
    'footer_terms_of_service' => 'Terms of Service',
    'footer_privacy_policy' => 'Privacy Policy',
    'footer_refund_policy' => 'Refund Policy',
    'footer_faq' => 'FAQ',
    'footer_newsletter_title' => 'Subscribe to Our Newsletter',
    'footer_newsletter_description' => 'Get the latest news and exclusive offers.',
    'footer_email_placeholder' => 'Your email address',
    'footer_subscribe_button' => 'Subscribe',
    'footer_all_rights_reserved' => 'All rights reserved',
    'footer_made_with_love' => 'Made with love',
    'footer_in_morocco' => 'in Morocco',

    // User Dashboard
    'user_dashboard_title' => 'User Dashboard',
    'user_dashboard_welcome' => 'Welcome back, :name!',
    'user_dashboard_welcome_subtitle' => 'Manage your account and track your activities',
    'user_dashboard_search_placeholder' => 'Search...',
    'user' => 'User',

    // Navigation
    'user_dashboard_main_nav' => 'Main Navigation',
    'user_dashboard_settings_nav' => 'Settings & Support',
    'user_dashboard_overview' => 'Dashboard',
    'user_dashboard_profile' => 'Profile',
    'user_dashboard_orders' => 'My Orders',
    'user_dashboard_wishlist' => 'Wishlist',
    'user_dashboard_payments' => 'Payment Methods',
    'user_dashboard_notifications' => 'Notifications',
    'user_dashboard_settings' => 'Settings',
    'user_dashboard_help' => 'Help & Support',

    // Dashboard Overview
    'user_dashboard_stats_title' => 'Your Statistics',
    'user_dashboard_total_orders' => 'Total Orders',
    'user_dashboard_pending_orders' => 'Pending Orders',
    'user_dashboard_completed_orders' => 'Completed Orders',
    'user_dashboard_total_spent' => 'Total Spent',
    'user_dashboard_recent_orders' => 'Recent Orders',
    'user_dashboard_view_all_orders' => 'View All Orders',
    'user_dashboard_no_orders' => 'No orders yet',
    'user_dashboard_start_shopping' => 'Start Shopping',

    // Status translations
    'completed' => 'Completed',
    'pending' => 'Pending',
    'cancelled' => 'Cancelled',
    'confirmed' => 'Confirmed',
    'shipped' => 'Shipped',
    'delivered' => 'Delivered',

    // Order Management
    'orders' => 'Orders',
    'order_management' => 'Order Management',
    'create_order' => 'Create Order',
    'edit_order' => 'Edit Order',
    'view_order' => 'View Order',
    'delete_order' => 'Delete Order',
    'order_deleted_successfully' => 'Order deleted successfully',
    'delete_order_confirmation' => 'Are you sure you want to delete this order? This action cannot be undone.',
    'search_and_filter_orders' => 'Search and filter orders',
    'search_orders' => 'Search orders by client name, phone, tracking number...',
    'no_orders_found' => 'No orders found',
    'all_statuses' => 'All Statuses',
    'order_id' => 'Order ID',
    'client_name' => 'Client Name',
    'product' => 'Product',
    'user' => 'User',
    'sale_price' => 'Sale Price',
    'profit' => 'Profit',
    'status' => 'Status',
    'back_to_orders' => 'Back to Orders',
    'back_to_order' => 'Back to Order',
    'edit' => 'Edit',

    // Profile Settings
    'profile_settings' => 'Profile Settings',
    'profile_information' => 'Profile Information',
    'profile_information_description' => 'Update your personal information and contact details',
    'payment_information' => 'Payment Information',
    'payment_information_description' => 'Manage your payment methods and banking details',
    'profile_change_photo' => 'Change Photo',
    'profile_update_photo_title' => 'Update Profile Photo',
    'profile_update_photo_description' => 'Choose a new photo for your profile. Maximum file size is 5MB.',
    'profile_update_photo' => 'Update Photo',
    'profile_photo_updated_success' => 'Profile photo updated successfully!',
    'profile_photo_update_error' => 'Failed to update profile photo. Please try again.',
    'profile_photo_invalid_type' => 'Please select a valid image file (JPEG, PNG, JPG, GIF).',
    'profile_photo_too_large' => 'Image file is too large. Maximum size is 5MB.',
    'profile_updated_success' => 'Profile updated successfully!',
    'profile_update_error' => 'Failed to update profile. Please try again.',
    'bio' => 'Bio',
    'bio_placeholder' => 'Tell us about yourself...',
    'select_gender' => 'Select Gender',
    'male' => 'Male',
    'female' => 'Female',
    'select_country' => 'Select Country',
    'morocco' => 'Morocco',
    'algeria' => 'Algeria',
    'tunisia' => 'Tunisia',
    'select_payment_method' => 'Select Payment Method',
    'cash' => 'Cash',
    'bank_transfer' => 'Bank Transfer',
    'save_changes' => 'Save Changes',
    'saving' => 'Saving...',
    'saved' => 'Saved',
    'uploading' => 'Uploading...',
    'cancel' => 'Cancel',
    'email_unverified' => 'Your email address is unverified.',
    'resend_verification' => 'Click here to resend the verification email.',
    'verification_sent' => 'A new verification link has been sent to your email address.',

    // Payment Settings
    'payment_settings' => 'Payment Settings',
    'payment_updated_success' => 'Payment settings updated successfully!',
    'payment_update_error' => 'Failed to update payment settings. Please try again.',
    'payment_method_description' => 'Choose how you prefer to receive payments for your sales.',
    'bank_details' => 'Bank Details',
    'bank_name_placeholder' => 'Enter your bank name',
    'rib_number_placeholder' => 'Enter your RIB number',
    'bank_details_note' => 'These details will be used for bank transfers. Make sure they are accurate.',
    'save_payment_settings' => 'Save Payment Settings',
    'other_bank' => 'Other Bank',
    'select_bank' => 'Select your bank',
    'bank_name_required' => 'Bank name is required for bank transfers',
    'custom_bank_name' => 'Bank Name',
    'enter_bank_name' => 'Enter your bank name',
    'custom_bank_name_required' => 'Please enter the bank name',
    'rib_number_required' => 'RIB number is required for bank transfers',
    'cash_payment_description' => 'Receive payments in cash',
    'bank_payment_description' => 'Receive payments via bank transfer',

    // User Management
    'users' => 'Users',
    'user_management' => 'User Management',
    'create_user' => 'Create User',
    'edit_user' => 'Edit User',
    'view_user' => 'View User',
    'delete_user' => 'Delete User',
    'user_created_successfully' => 'User created successfully',
    'user_updated_successfully' => 'User updated successfully',
    'user_deleted_successfully' => 'User deleted successfully',
    'user_activated_successfully' => 'User activated successfully',
    'user_deactivated_successfully' => 'User deactivated successfully',
    'search_users' => 'Search users...',
    'no_users_found' => 'No users found',
    'total_users' => 'Total Users',
    'active_users' => 'Active Users',
    'inactive_users' => 'Inactive Users',
    'user_status' => 'Status',
    'activate_user' => 'Activate User',
    'deactivate_user' => 'Deactivate User',
    'user_details' => 'User Details',
    'personal_information' => 'Personal Information',
    'contact_information' => 'Contact Information',
    'payment_information' => 'Payment Information',
    'account_status' => 'Account Status',
    'created_at' => 'Created At',
    'updated_at' => 'Updated At',
    'actions' => 'Actions',
    'per_page' => 'Per Page',
    'showing_results' => 'Showing :from to :to of :total results',
    'filters' => 'Filters',
    'search_and_filter_users' => 'Search and filter users',
    'delete_user_confirmation' => 'Are you sure you want to delete this user? This action cannot be undone.',
    'create_new_user_account' => 'Create a new user account with all necessary information.',
    'enter_user_personal_details' => 'Enter the user\'s personal information.',
    'enter_user_contact_details' => 'Enter the user\'s contact information.',
    'configure_user_payment_method' => 'Configure how the user will receive payments.',
    'configure_user_account_settings' => 'Set up the user\'s account credentials and status.',
    'account_settings' => 'Account Settings',
    'active_account' => 'Active Account',
    'creating' => 'Creating...',
    'previous' => 'Previous',
    'next' => 'Next',
    'edit_user_account_information' => 'Edit user account information and settings.',
    'update_user_personal_details' => 'Update the user\'s personal information.',
    'update_user_contact_details' => 'Update the user\'s contact information.',
    'update_user_payment_method' => 'Update how the user will receive payments.',
    'update_user_account_settings' => 'Update the user\'s account credentials and status.',
    'new_password' => 'New Password',
    'optional' => 'Optional',
    'leave_blank_to_keep_current' => 'Leave blank to keep current password',
    'confirm_new_password' => 'Confirm New Password',
    'updating' => 'Updating...',
    'update_user' => 'Update User',
    'member_since' => 'Member since',
    'deactivate' => 'Deactivate',
    'activate' => 'Activate',
    'location' => 'Location',
    'not_specified' => 'Not specified',
    'email_verified_at' => 'Email Verified At',

    // City Management
    'cities' => 'Cities',
    'city_management' => 'City Management',
    'create_city' => 'Create City',
    'edit_city' => 'Edit City',
    'view_city' => 'View City',
    'delete_city' => 'Delete City',
    'city_created_successfully' => 'City created successfully',
    'city_updated_successfully' => 'City updated successfully',
    'city_deleted_successfully' => 'City deleted successfully',
    'search_cities' => 'Search cities...',
    'no_cities_found' => 'No cities found',
    'total_cities' => 'Total Cities',
    'city_details' => 'City Details',
    'city_information' => 'City Information',
    'shipping_information' => 'Shipping Information',
    'ar_name' => 'Arabic Name',
    'en_name' => 'English Name',
    'fr_name' => 'French Name',
    'shipping_cost' => 'Shipping Cost',
    'delete_city_confirmation' => 'Are you sure you want to delete this city? This action cannot be undone.',
    'create_new_city' => 'Create a new city with multilingual names and shipping cost.',
    'enter_city_details' => 'Enter the city information in all supported languages.',
    'configure_shipping_cost' => 'Set the shipping cost for this city.',
    'update_city_information' => 'Update the city information and shipping cost.',
    'city_names' => 'City Names',
    'currency_symbol' => 'MAD',
    'search_and_filter_cities' => 'Search and filter cities',
    'shipping_cost_description' => 'Enter the shipping cost for orders to this city',
    'update_city' => 'Update City',
    'created_on' => 'Created on',
    'multilingual_city_names' => 'City names in all supported languages',
    'shipping_cost_details' => 'Shipping cost information for this city',
    'shipping_cost_per_order' => 'Cost per order to this city',
    'city_system_details' => 'System information and timestamps',
    'city_id' => 'City ID',
    'rib_length_error' => 'RIB number must be exactly 24 characters',
    'rib_format_error' => 'RIB number must contain only letters and numbers',
    '24_characters' => '24 characters',
    'characters' => 'characters',
    'format' => 'Format',

    // Categories Management
    'categories' => 'Categories',
    'category_management' => 'Category Management',
    'create_category' => 'Create Category',
    'edit_category' => 'Edit Category',
    'view_category' => 'View Category',
    'delete_category' => 'Delete Category',
    'category_created_successfully' => 'Category created successfully',
    'category_updated_successfully' => 'Category updated successfully',
    'category_deleted_successfully' => 'Category deleted successfully',
    'delete_category_confirmation' => 'Are you sure you want to delete this category? This action cannot be undone.',
    'create_new_category' => 'Create a new category with multilingual names and descriptions.',
    'edit_category_details' => 'Edit the category information and settings.',
    'category_names' => 'Category Names',
    'category_descriptions' => 'Category Descriptions',
    'category_image' => 'Category Image',
    'category_settings' => 'Category Settings',
    'enter_category_names_in_all_languages' => 'Enter the category names in all supported languages.',
    'enter_category_descriptions_optional' => 'Enter category descriptions in all languages (optional).',
    'upload_category_image_optional' => 'Upload a category image (optional).',
    'configure_category_settings' => 'Configure category status and display order.',
    'ar_description' => 'Arabic Description',
    'en_description' => 'English Description',
    'fr_description' => 'French Description',
    'image' => 'Image',
    'image_preview' => 'Image Preview',
    'current_image' => 'Current Image',
    'no_image_uploaded' => 'No image uploaded',
    'image_upload_description' => 'Upload an image file (JPG, PNG, GIF). Maximum size: 2MB.',
    'sort_order' => 'Sort Order',
    'sort_order_description' => 'Lower numbers appear first in listings.',
    'slug' => 'Slug',
    'status' => 'Status',
    'active' => 'Active',
    'inactive' => 'Inactive',
    'search_categories' => 'Search categories...',
    'search_and_filter_categories' => 'Search and filter categories',
    'no_categories_found' => 'No categories found',
    'multilingual_category_names' => 'Category names in all supported languages',
    'multilingual_category_descriptions' => 'Category descriptions in all supported languages',
    'category_visual_representation' => 'Visual representation of the category',
    'category_configuration_details' => 'Category status and ordering configuration',
    'category_system_details' => 'System information and timestamps',
    'category_id' => 'Category ID',
    'no_description_provided' => 'No description provided',
    'creating' => 'Creating...',
    'updating' => 'Updating...',
    'update_category' => 'Update Category',

    // Products Management
    'products' => 'Products',
    'product_management' => 'Product Management',
    'create_product' => 'Create Product',
    'edit_product' => 'Edit Product',
    'view_product' => 'View Product',
    'delete_product' => 'Delete Product',
    'product_created_successfully' => 'Product created successfully',
    'product_updated_successfully' => 'Product updated successfully',
    'product_deleted_successfully' => 'Product deleted successfully',
    'delete_product_confirmation' => 'Are you sure you want to delete this product? This action cannot be undone.',
    'create_new_product' => 'Create a new product with all details and images.',
    'edit_product_details' => 'Edit the product information and settings.',
    'basic_information' => 'Basic Information',
    'pricing_inventory' => 'Pricing & Inventory',
    'product_images' => 'Product Images',
    'product_settings' => 'Product Settings',
    'enter_product_basic_details' => 'Enter the basic product information.',
    'set_product_price_and_stock' => 'Set the product price and stock quantity.',
    'upload_product_images' => 'Upload product images and gallery.',
    'configure_product_settings' => 'Configure product status and features.',
    'product_name' => 'Product Name',
    'enter_product_name' => 'Enter the product name',
    'sku' => 'SKU',
    'sku_description' => 'Unique product identifier (Stock Keeping Unit)',
    'enter_product_description' => 'Enter the product description',
    'details' => 'Details',
    'enter_product_details_optional' => 'Enter additional product details (optional)',
    'product_details_description' => 'Additional detailed information about the product',
    'price' => 'Price',
    'stock_quantity' => 'Stock Quantity',
    'stock' => 'Stock',
    'select_product_categories' => 'Select one or more categories for this product.',
    'select_at_least_one_category' => 'Please select at least one category.',
    'main_image' => 'Main Image',
    'main_image_description' => 'Upload the main product image (required). Maximum size: 2MB.',
    'main_image_preview' => 'Main Image Preview',
    'current_main_image' => 'Current Main Image',
    'additional_images' => 'Additional Images',
    'additional_images_description' => 'Upload additional product images (optional). Maximum size: 2MB each.',
    'additional_images_preview' => 'Additional Images Preview',
    'current_additional_images' => 'Current Additional Images',
    'new_additional_images_preview' => 'New Additional Images Preview',
    'no_main_image' => 'No main image uploaded',
    'published' => 'Published',
    'draft' => 'Draft',
    'featured_product' => 'Featured Product',
    'featured' => 'Featured',
    'not_featured' => 'Not Featured',
    'search_products' => 'Search products...',
    'search_and_filter_products' => 'Search and filter products',
    'all_statuses' => 'All Statuses',
    'all_categories' => 'All Categories',
    'no_products_found' => 'No products found',
    'in_stock' => 'In Stock',
    'out_of_stock' => 'Out of Stock',
    'units' => 'units',
    'product_visual_gallery' => 'Product images and visual gallery',
    'product_basic_details' => 'Basic product information and specifications',
    'description_details' => 'Description & Details',
    'product_description_and_details' => 'Product description and detailed information',
    'product_categories' => 'Product categories and classifications',
    'no_categories_assigned' => 'No categories assigned',
    'product_configuration_details' => 'Product status and feature configuration',
    'product_system_details' => 'System information and timestamps',
    'product_id' => 'Product ID',
    'update_product' => 'Update Product',

];

