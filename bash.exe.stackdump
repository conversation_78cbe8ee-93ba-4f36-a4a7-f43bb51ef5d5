Stack trace:
Frame         Function      Args
0007FFFF5810  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFF5810, 0007FFFF4710) msys-2.0.dll+0x1FE8E
0007FFFF5810  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF5AE8) msys-2.0.dll+0x67F9
0007FFFF5810  000210046832 (000210286019, 0007FFFF56C8, 0007FFFF5810, 000000000000) msys-2.0.dll+0x6832
0007FFFF5810  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFF5810  000210068E24 (0007FFFF5820, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFF5AF0  00021006A225 (0007FFFF5820, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFCAB310000 ntdll.dll
7FFCAA160000 KERNEL32.DLL
7FFCA8B70000 KERNELBASE.dll
7FFCA9DD0000 USER32.dll
7FFCA8EC0000 win32u.dll
7FFCA9C50000 GDI32.dll
7FFCA9050000 gdi32full.dll
7FFCA8AD0000 msvcp_win.dll
7FFCA89D0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFCA9F70000 advapi32.dll
7FFCA9BB0000 msvcrt.dll
7FFCAA650000 sechost.dll
7FFCAB140000 RPCRT4.dll
7FFCA89A0000 bcrypt.dll
7FFCA8260000 CRYPTBASE.DLL
7FFCA92A0000 bcryptPrimitives.dll
7FFCA9B80000 IMM32.DLL
