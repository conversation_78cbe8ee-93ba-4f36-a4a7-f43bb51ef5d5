import React from 'react';
import { cn } from '@/lib/utils';

interface RichTextViewerProps {
  content: string;
  className?: string;
}

export function RichTextViewer({ content, className }: RichTextViewerProps) {
  if (!content) {
    return null;
  }

  return (
    <div 
      className={cn(
        'prose prose-sm sm:prose-base lg:prose-lg xl:prose-xl max-w-none',
        'prose-headings:font-semibold prose-headings:text-foreground',
        'prose-p:text-foreground prose-p:leading-relaxed',
        'prose-a:text-primary prose-a:no-underline hover:prose-a:underline',
        'prose-strong:text-foreground prose-strong:font-semibold',
        'prose-em:text-foreground',
        'prose-code:text-foreground prose-code:bg-muted prose-code:px-1 prose-code:py-0.5 prose-code:rounded',
        'prose-pre:bg-muted prose-pre:text-foreground',
        'prose-blockquote:text-foreground prose-blockquote:border-l-primary',
        'prose-hr:border-border',
        'prose-ul:text-foreground prose-ol:text-foreground',
        'prose-li:text-foreground',
        'prose-table:text-foreground',
        'prose-th:text-foreground prose-th:border-border',
        'prose-td:text-foreground prose-td:border-border',
        className
      )}
      dangerouslySetInnerHTML={{ __html: content }}
    />
  );
}
